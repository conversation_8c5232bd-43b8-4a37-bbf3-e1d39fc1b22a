C:\Dev\Camera_Editor\Main\main.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\main.obj
C:\Dev\Camera_Editor\Main\app_lifecycle.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\app_lifecycle.obj
C:\Dev\Camera_Editor\Services\CameraService.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\CameraService.obj
C:\Dev\Camera_Editor\Services\InferenceService.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\InferenceService.obj
C:\Dev\Camera_Editor\Services\WebServerService.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\WebServerService.obj
C:\Dev\Camera_Editor\Services\RecordingService.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\RecordingService.obj
C:\Dev\Camera_Editor\Services\StereoReconstructionService.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\StereoReconstructionService.obj
C:\Dev\Camera_Editor\Services\DataLoggingService.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\DataLoggingService.obj
C:\Dev\Camera_Editor\Services\CalibrationService.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\CalibrationService.obj
C:\Dev\Camera_Editor\Services\HighlightService.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\HighlightService.obj
C:\Dev\Camera_Editor\Camera\hik.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\hik.obj
C:\Dev\Camera_Editor\Camera\dualEye.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\dualEye.obj
C:\Dev\Camera_Editor\Camera\detect.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\detect.obj
C:\Dev\Camera_Editor\Deploy\model.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\model.obj
C:\Dev\Camera_Editor\Deploy\utils\utils.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\utils.obj
C:\Dev\Camera_Editor\Utils\math_utils.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\math_utils.obj
C:\Dev\Camera_Editor\Utils\DebugConfig.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\DebugConfig.obj
C:\Dev\Camera_Editor\Web\backend\server.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\server.obj
C:\Dev\Camera_Editor\Deploy\yolo.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\yolo.obj
C:\Dev\Camera_Editor\Deploy\infer\backend.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\backend.obj
C:\Dev\Camera_Editor\Deploy\core\core.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\core.obj
C:\Dev\Camera_Editor\Deploy\core\buffer.cpp;C:\Dev\Camera_Editor\Camera_Editor.dir\Release\buffer.obj
