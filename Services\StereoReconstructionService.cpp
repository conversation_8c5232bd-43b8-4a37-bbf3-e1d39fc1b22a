#include "StereoReconstructionService.hpp"
#include "../Utils/utf8_utils.hpp"
#include "../Utils/DebugConfig.hpp"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <vector>

// Helper function to calculate dot product
double dot_product(const std::vector<double>& a, const std::vector<double>& b) {
    double result = 0.0;
    for (size_t i = 0; i < a.size(); ++i) {
        result += a[i] * b[i];
    }
    return result;
}

StereoReconstructionService::StereoReconstructionService(
    std::shared_ptr<SharedData> shared_data,
    Services::DataLoggingService* data_logging_service,
    int image_width, 
    int image_height
) : m_sharedData(std::move(shared_data)), 
    m_dataLoggingService(data_logging_service),
    m_wasBallDetectedLastFrame(false) {
    
    // 初始化双目视觉系统
    m_dualEye = std::make_unique<DUE::C_DualEye>(image_width, image_height);
    
    // UTF8Utils::println("🔧 立体视觉重建服务已初始化 (" +
    //                   std::to_string(image_width) + "x" + std::to_string(image_height) + ")");
}

void StereoReconstructionService::initializeROIFeatures() {
    m_roiPredictor = std::make_unique<DynamicROIPredictor>();
    m_roiEnabled = true;
    m_sharedData->setROIProcessingMode(true);
    UTF8Utils::println("🎯 ROI预测功能已启用");
}

void StereoReconstructionService::setROIEnabled(bool enabled) {
    m_roiEnabled = enabled;
    m_sharedData->setROIProcessingMode(enabled);
    if (enabled) {
        UTF8Utils::println("🎯 ROI预测功能已启用");
    } else {
        UTF8Utils::println("🎯 ROI预测功能已禁用");
    }
}

void StereoReconstructionService::calculateAndStoreSpeed(const BallPosition3D& latest_position) {
    auto current_time = std::chrono::high_resolution_clock::now();

    // 检测帧重复：如果新位置与最后一个位置过于接近，可能是重复帧
    if (!m_positionHistory.empty()) {
        const auto& last_pos = m_positionHistory.back().point;
        const auto& new_pos = latest_position.world_position;

        float position_change = std::sqrt(
            std::pow(new_pos.x - last_pos.x, 2) +
            std::pow(new_pos.y - last_pos.y, 2) +
            std::pow(new_pos.z - last_pos.z, 2)
        );

        // 如果位置变化小于1mm，可能是重复帧
        if (position_change < 0.001f) {
            auto time_diff = std::chrono::duration<double>(current_time - m_positionHistory.back().timestamp).count();
            DEBUG_BALL_SPEED("检测到可能的重复帧 - 位置变化: " +
                           std::to_string(position_change * 1000) + "mm, 时间间隔: " +
                           std::to_string(time_diff * 1000) + "ms");

            // 如果时间间隔也很小（<2ms），跳过此帧
            if (time_diff < 0.002) {
                DEBUG_BALL_SPEED("跳过重复帧，保持当前球速");
                return;
            }
        }
    }

    // 1. Add new point to history
    m_positionHistory.push_back({current_time, latest_position.world_position});

    // 2. Maintain history size
    if (m_positionHistory.size() > m_historySize) {
        m_positionHistory.pop_front();
    }

    // 3. Check if we have enough data for SG filter (使用优化后的窗口大小)
    const int optimized_window_size = 5;  // 优化：从7减少到5，适合210FPS高速运动
    if (m_positionHistory.size() < optimized_window_size) {
        DEBUG_BALL_SPEED("历史数据不足: " + std::to_string(m_positionHistory.size()) +
                        "/" + std::to_string(optimized_window_size) + " 点，设置速度为0");
        m_sharedData->setBallSpeed(0.0);
        return;
    }

    // Use the most recent `optimized_window_size` points for calculation
    auto start_it = m_positionHistory.end() - optimized_window_size;

    std::vector<double> t_data, x_data, y_data, z_data;
    std::vector<double> dt_values; // 存储实际时间间隔
    t_data.reserve(optimized_window_size);
    x_data.reserve(optimized_window_size);
    y_data.reserve(optimized_window_size);
    z_data.reserve(optimized_window_size);

    auto first_timestamp = start_it->timestamp;
    for (auto it = start_it; it != m_positionHistory.end(); ++it) {
        t_data.push_back(std::chrono::duration<double>(it->timestamp - first_timestamp).count());
        x_data.push_back(it->point.x);
        y_data.push_back(it->point.y);
        z_data.push_back(it->point.z);
    }

    // 计算实际相邻帧之间的时间间隔
    for (auto it = start_it + 1; it != m_positionHistory.end(); ++it) {
        auto dt = std::chrono::duration<double>(it->timestamp - (it-1)->timestamp).count();
        dt_values.push_back(dt);
    }

    try {
        // 验证时间间隔的有效性
        if (dt_values.empty()) {
            DEBUG_BALL_SPEED("时间间隔计算失败，设置速度为0");
            m_sharedData->setBallSpeed(0.0);
            return;
        }

        // 计算时间间隔统计信息
        double min_dt = *std::min_element(dt_values.begin(), dt_values.end());
        double max_dt = *std::max_element(dt_values.begin(), dt_values.end());
        double sum_dt = std::accumulate(dt_values.begin(), dt_values.end(), 0.0);
        double avg_dt = sum_dt / dt_values.size();

        // 智能时间间隔处理策略
        const double MAX_NORMAL_INTERVAL = 0.2;      // 200ms - 正常处理间隔
        const double MAX_ACCEPTABLE_INTERVAL = 1.0;   // 1秒 - 可接受的间隔
        const double MAX_REASONABLE_INTERVAL = 3.0;   // 3秒 - 合理的重连间隔

        // 统计时间间隔分布
        int normal_intervals = 0;
        int acceptable_intervals = 0;
        int problematic_intervals = 0;

        for (double dt : dt_values) {
            if (dt <= MAX_NORMAL_INTERVAL) normal_intervals++;
            else if (dt <= MAX_ACCEPTABLE_INTERVAL) acceptable_intervals++;
            else problematic_intervals++;
        }

        // 如果大部分时间间隔都是异常的，说明这是球重新出现
        if (problematic_intervals > dt_values.size() / 2) {
            DEBUG_BALL_SPEED("检测到球重新出现 - 异常间隔: " + std::to_string(problematic_intervals) +
                           "/" + std::to_string(dt_values.size()) + ", 最大: " +
                           std::to_string(max_dt * 1000) + "ms, 重新开始轨迹跟踪");

            // 保留最近的2个点，丢弃旧的异常数据
            if (m_positionHistory.size() >= 2) {
                auto recent_points = std::vector<TimedPoint3f>(
                    m_positionHistory.end() - 2, m_positionHistory.end()
                );
                m_positionHistory.clear();
                for (const auto& point : recent_points) {
                    m_positionHistory.push_back(point);
                }
                DEBUG_BALL_SPEED("保留最近2个检测点，继续跟踪");
            } else {
                m_positionHistory.clear();
                m_positionHistory.push_back({current_time, latest_position.world_position});
                DEBUG_BALL_SPEED("历史数据不足，重新开始");
            }
            m_sharedData->setBallSpeed(0.0);
            return;
        }

        // 如果只有少数异常间隔，使用中位数时间间隔进行计算
        if (problematic_intervals > 0 && problematic_intervals <= dt_values.size() / 2) {
            // 计算中位数时间间隔，排除异常值
            std::vector<double> normal_dt_values;
            for (double dt : dt_values) {
                if (dt <= MAX_ACCEPTABLE_INTERVAL) {
                    normal_dt_values.push_back(dt);
                }
            }

            if (!normal_dt_values.empty()) {
                std::sort(normal_dt_values.begin(), normal_dt_values.end());
                double median_dt = normal_dt_values[normal_dt_values.size() / 2];
                avg_dt = median_dt;  // 使用中位数替代平均值

                DEBUG_BALL_SPEED("使用中位数时间间隔: " + std::to_string(median_dt * 1000) +
                               "ms (排除" + std::to_string(problematic_intervals) + "个异常值)");
            }
        }

        // 正常情况的调试信息
        if (normal_intervals == dt_values.size()) {
            DEBUG_BALL_SPEED("时间间隔正常 - 平均: " + std::to_string(avg_dt * 1000) + "ms");
        }

        if (avg_dt < 1e-6) { // Avoid division by zero
            DEBUG_BALL_SPEED("平均时间间隔过小: " + std::to_string(avg_dt) + "s，设置速度为0");
            m_sharedData->setBallSpeed(0.0);
            return;
        }

        // 4. Compute SG coefficients for the 1st derivative (使用优化后的参数)
        const int optimized_poly_order = 1;   // 优化：从2减少到1，减少过度平滑
        auto coeffs = SignalProcessing::compute_sg_coeffs(optimized_window_size, optimized_poly_order, 1);

        // 5. Apply filter to get derivatives (velocity components)
        // The derivative is applied to the central point of the window
        double vx = dot_product(coeffs, x_data) / avg_dt;
        double vy = dot_product(coeffs, y_data) / avg_dt;
        double vz = dot_product(coeffs, z_data) / avg_dt;

        // 6. Calculate speed magnitude and store it
        double speed = std::sqrt(vx * vx + vy * vy + vz * vz);

        // 详细调试信息（只在球速调试模式下显示）
        DEBUG_BALL_SPEED("计算详情 - 历史点: " + std::to_string(m_positionHistory.size()) +
                        ", 时间间隔: " + std::to_string(avg_dt * 1000) + "ms(平均), " +
                        "速度分量: vx=" + std::to_string(vx) + ", vy=" + std::to_string(vy) +
                        ", vz=" + std::to_string(vz) + ", 最终速度: " + std::to_string(speed) + " m/s");

        // 检查速度合理性（乒乓球最高速度通常不超过50 m/s）
        if (speed > 50.0) {
            DEBUG_BALL_SPEED("计算出异常高速: " + std::to_string(speed) + " m/s，可能存在计算错误");
        } else if (speed > 20.0) {
            DEBUG_BALL_SPEED("检测到高速球: " + std::to_string(speed) + " m/s");
        }

        // 简洁的球速信息
        DEBUG_BALL_SPEED("球速: " + std::to_string(speed) + " m/s");

        m_sharedData->setBallSpeed(speed);

        // 注释：数据记录已移至独立的数据记录路径，不再在此处重复记录
        // 这样可以避免数据记录与三维重建成功与否的耦合

        // === 新增：ROI预测逻辑 ===
        if (m_roiEnabled && m_roiPredictor) {
            updateROIPredictionsUsingDualEye(latest_position);
        }

    } catch (const std::exception& e) {
        UTF8Utils::println(std::string("❌ SG Filter speed calculation error: ") + e.what());
        m_sharedData->setBallSpeed(0.0);
    }
}

bool StereoReconstructionService::processDetections(
    const std::map<std::string, std::vector<Yolo::Detection>>& left_detections,
    const std::map<std::string, std::vector<Yolo::Detection>>& right_detections,
    std::chrono::milliseconds timestamp
) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    try {
        std::vector<BallPosition3D> ball_positions;
        
        // 遍历所有目标类别，进行跨视图匹配
        for (const auto& class_name : m_targetClasses) {
            // 使用DualEye库进行特征匹配
            auto matched_pairs = DUE::classifyMultiple(
                left_detections, 
                right_detections, 
                class_name, 
                m_matchingThreshold
            );

            if (!matched_pairs.empty()) {
                // 进行三维重建，计算世界坐标
                auto world_points = m_dualEye->calP3inWorld(matched_pairs);
                
                // 将结果转换为BallPosition3D格式
                for (size_t i = 0; i < world_points.size(); ++i) {
                    const auto& world_point = world_points[i];
                    
                    // 计算置信度（取匹配对中的较小值）
                    float confidence = std::min<float>(
                        matched_pairs[i].confLeft, 
                        matched_pairs[i].confRight
                    );
                    
                    ball_positions.emplace_back(
                        static_cast<int>(ball_positions.size()), // 球的ID
                        world_point,
                        confidence,
                        timestamp
                    );
                }

                // --- 新增: 更新2D轨迹 ---
                // 在这里 matched_pairs 是可见的，并且与重建的球一一对应
                for (const auto& ball_match : matched_pairs) {
                    // 假设相机ID 1 是左, 2 是右
                    m_2d_trajectories[1].push_back(ball_match.uvLeft);
                    if (m_2d_trajectories[1].size() > m_max2dTrajectorySize) {
                        m_2d_trajectories[1].pop_front();
                    }

                    m_2d_trajectories[2].push_back(ball_match.uvRight);
                    if (m_2d_trajectories[2].size() > m_max2dTrajectorySize) {
                        m_2d_trajectories[2].pop_front();
                    }
                }
            }
        }
        
        // 将结果存储到SharedData中
        m_sharedData->setBallPositions3D(ball_positions);

        // 如果成功重建，计算并存储速度，并更新轨迹
        if (!ball_positions.empty()) {
            // 统计成功的3D重建次数
            m_sharedData->increment3DReconstructionCount();
            // 我们假设追踪列表中的第一个球
            calculateAndStoreSpeed(ball_positions[0]);

            // --- 新增：更新轨迹 ---
            if (!m_wasBallDetectedLastFrame) {
                // 如果上一帧没有球，而这一帧有，说明是新轨迹的开始
                m_currentTrajectory.clear();
                m_2d_trajectories.clear(); // 清空2D轨迹
            }
            m_wasBallDetectedLastFrame = true;

            m_currentTrajectory.push_back(ball_positions[0]);

            // --- 新增: 更新2D轨迹 ---
            // 我们需要原始的匹配对来获取2D坐标
            // --- 此处代码块已被移动到正确的位置 ---

            // 保持轨迹队列长度
            if (m_currentTrajectory.size() > m_maxTrajectorySize) {
                m_currentTrajectory.erase(m_currentTrajectory.begin());
            }

            // 将更新后的轨迹存入共享数据
            m_sharedData->setTrajectory(m_currentTrajectory);
            m_sharedData->set2dTrajectories(m_2d_trajectories); // 更新2D轨迹到SharedData
            // --------------------

        } else {
            // 没有检测到球时的智能处理策略
            auto current_time = std::chrono::high_resolution_clock::now();

            // 检查历史记录中最后一个点的时间
            if (!m_positionHistory.empty()) {
                auto last_detection_time = m_positionHistory.back().timestamp;
                auto time_since_last_detection = std::chrono::duration<double>(current_time - last_detection_time).count();

                // 根据实际系统性能调整球丢失的容忍时间
                // 考虑到实际处理间隔约100ms，给予更长的容忍时间
                const double BALL_LOSS_TOLERANCE = 1.0;  // 1秒容忍时间

                if (time_since_last_detection > BALL_LOSS_TOLERANCE) {
                    DEBUG_BALL_SPEED("球丢失超过" + std::to_string(BALL_LOSS_TOLERANCE) +
                                   "秒，清空历史记录。丢失时间: " +
                                   std::to_string(time_since_last_detection * 1000) + "ms");
                    m_positionHistory.clear();
                } else {
                    DEBUG_BALL_SPEED("球暂时丢失 " + std::to_string(time_since_last_detection * 1000) +
                                   "ms，保留历史记录等待重新检测");
                }
            }

            m_sharedData->setBallSpeed(0.0);
            m_wasBallDetectedLastFrame = false;
            // 当球消失时，我们暂时不清除轨迹，以便前端可以继续显示最后一段路径。
            // 只有当新的轨迹开始时（见上文）才清除。
        }
        
        // 更新统计信息
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        updateStatistics(!ball_positions.empty(), duration.count() / 1000.0);
        
        return !ball_positions.empty();
        
    } catch (const std::exception& e) {
        UTF8Utils::println("❌ 三维重建处理错误: " + std::string(e.what()));
        
        // 更新失败统计
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        updateStatistics(false, duration.count() / 1000.0);
        
        return false;
    }
}

bool StereoReconstructionService::processLatestDetections() {
    try {
        // 从SharedData获取最新的立体检测结果
        auto [left_detections, right_detections] = m_sharedData->getStereoDetections(1, 2);
        
        // 检查是否有有效数据
        if (left_detections.empty() || right_detections.empty()) {
            // 调试信息: 清楚地表明为何跳过处理
            if (left_detections.empty() && right_detections.empty()) {
                // 仅在两个都为空时安静地跳过，这是正常情况
            } else {
                 // UTF8Utils::println("[调试] 跳过三维重建：仅单侧相机有检测结果 (左: " + 
                 //                  std::to_string(left_detections.size()) + ", 右: " + std::to_string(right_detections.size()) + ")");
            }
            return false;
        }
        
        // 获取当前时间戳
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch());
        
        // 调用主要的处理方法
        return processDetections(left_detections, right_detections, timestamp);
        
    } catch (const std::exception& e) {
        // UTF8Utils::println("❌ processLatestDetections 错误: " + std::string(e.what()));
        return false;
    }
}

void StereoReconstructionService::updateStatistics(bool success, double processing_time_ms) {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    m_stats.total_processed++;
    
    if (success) {
        m_stats.successful_reconstructions++;
    } else {
        m_stats.failed_matches++;
    }
    
    // 计算平均处理时间（移动平均）
    double alpha = 0.1; // 平滑因子
    m_stats.avg_processing_time_ms = alpha * processing_time_ms + 
                                    (1.0 - alpha) * m_stats.avg_processing_time_ms;
}

void StereoReconstructionService::reloadCalibrationData() {
    if (m_dualEye) {
        UTF8Utils::println("🔄 StereoReconstructionService: 重新加载标定数据...");
        m_dualEye->reloadCalibrationData();
        UTF8Utils::println("✅ StereoReconstructionService: 标定数据重新加载完成");
    }
}

DetectionResult StereoReconstructionService::convertYoloDetections(
    const std::map<std::string, std::vector<Yolo::Detection>>& yolo_detections
) {
    DetectionResult result;

    for (const auto& [class_name, detections] : yolo_detections) {
        for (const auto& detection : detections) {
            result.boxes.emplace_back(cv::Rect(
                cv::Point(detection.left, detection.top),
                cv::Point(detection.right, detection.bottom)
            ));
            result.confidences.push_back(detection.conf);
            result.class_ids.push_back(detection.class_id);
            result.class_names.push_back(class_name);
        }
    }

    return result;
}

void StereoReconstructionService::updateROIPredictionsUsingDualEye(const BallPosition3D& latest_position) {
    double dt = 0.01;  // 10ms间隔预测

    // 更新轨迹预测器
    cv::Point3f cv_position(
        latest_position.world_position.x,
        latest_position.world_position.y,
        latest_position.world_position.z
    );
    m_roiPredictor->updateState(cv_position, dt);

    // 预测下一帧3D位置
    cv::Point3f predicted_3d = m_roiPredictor->predictNextPosition(dt);

    // 转换为MU::Point3f格式（与DualEye接口兼容）
    MU::Point3f predicted_mu(predicted_3d.x, predicted_3d.y, predicted_3d.z);

    // 使用当前实际位置进行投影（用于可见性判断）
    MU::Point3f current_mu(latest_position.world_position.x,
                          latest_position.world_position.y,
                          latest_position.world_position.z);
    auto current_projected = m_dualEye->projectWorldPoint(current_mu);
    cv::Point2f current_left = current_projected.first;
    cv::Point2f current_right = current_projected.second;

    // 使用预测位置进行投影（用于ROI生成）
    auto predicted_projected = m_dualEye->projectWorldPoint(predicted_mu);
    cv::Point2f predicted_left = predicted_projected.first;
    cv::Point2f predicted_right = predicted_projected.second;

    // 检查当前位置的可见性（决定是否生成ROI）
    cv::Size image_size = m_dualEye->imageSize;
    bool left_visible = (current_left.x >= 0 && current_left.x < image_size.width &&
                        current_left.y >= 0 && current_left.y < image_size.height);
    bool right_visible = (current_right.x >= 0 && current_right.x < image_size.width &&
                         current_right.y >= 0 && current_right.y < image_size.height);

    DEBUG_ROI("当前位置投影 - 左: (" + std::to_string(current_left.x) + "," +
             std::to_string(current_left.y) + "), 右: (" +
             std::to_string(current_right.x) + "," + std::to_string(current_right.y) + ")");
    DEBUG_ROI("预测位置投影 - 左: (" + std::to_string(predicted_left.x) + "," +
             std::to_string(predicted_left.y) + "), 右: (" +
             std::to_string(predicted_right.x) + "," + std::to_string(predicted_right.y) + ")");

    // 生成ROI数据
    cv::Rect left_roi, right_roi;
    float left_confidence = 0.0f, right_confidence = 0.0f;

    // 生成左摄像头ROI（使用预测位置）
    if (left_visible) {
        left_roi = generateROIAroundPoint(predicted_left, 150);
        left_confidence = 0.8f;
        m_sharedData->setROIPrediction(1, left_roi, left_confidence);
        DEBUG_ROI("左摄像头ROI: (" + std::to_string(left_roi.x) + "," +
                 std::to_string(left_roi.y) + "," + std::to_string(left_roi.width) +
                 "x" + std::to_string(left_roi.height) + ") [基于预测位置]");
    } else {
        left_roi = cv::Rect();
        left_confidence = 0.0f;
        m_sharedData->setROIPrediction(1, left_roi, left_confidence);
        DEBUG_ROI("左摄像头：当前位置不可见，使用全画面搜索");
    }

    // 生成右摄像头ROI（使用预测位置）
    if (right_visible) {
        right_roi = generateROIAroundPoint(predicted_right, 150);
        right_confidence = 0.8f;
        m_sharedData->setROIPrediction(2, right_roi, right_confidence);
        DEBUG_ROI("右摄像头ROI: (" + std::to_string(right_roi.x) + "," +
                 std::to_string(right_roi.y) + "," + std::to_string(right_roi.width) +
                 "x" + std::to_string(right_roi.height) + ") [基于预测位置]");
    } else {
        right_roi = cv::Rect();
        right_confidence = 0.0f;
        m_sharedData->setROIPrediction(2, right_roi, right_confidence);
        DEBUG_ROI("右摄像头：当前位置不可见，使用全画面搜索");
    }

    // 更新立体匹配状态
    bool stereo_matchable = left_visible && right_visible;
    m_sharedData->setStereoMatchable(stereo_matchable);

    // 调试输出
    DEBUG_ROI("立体匹配状态: " + std::string(stereo_matchable ? "可匹配" : "不可匹配") +
             " (左可见: " + std::string(left_visible ? "是" : "否") +
             ", 右可见: " + std::string(right_visible ? "是" : "否") + ")");

    // 创建并设置完整的StereoROIData
    SharedData::StereoROIData stereo_roi_data;
    stereo_roi_data.left_roi = left_roi;
    stereo_roi_data.right_roi = right_roi;
    stereo_roi_data.left_confidence = left_confidence;
    stereo_roi_data.right_confidence = right_confidence;
    stereo_roi_data.stereo_matchable = stereo_matchable;
    stereo_roi_data.timestamp = std::chrono::high_resolution_clock::now();

    // 设置双目ROI数据
    m_sharedData->setStereoROI(stereo_roi_data);

    DEBUG_ROI("已更新StereoROIData - 立体匹配: " + std::string(stereo_matchable ? "是" : "否"));

    // 验证对极约束（复用现有的匹配阈值）
    if (stereo_matchable) {
        float y_diff = std::abs(predicted_left.y - predicted_right.y);
        if (y_diff > m_matchingThreshold) {
            DEBUG_ROI("警告：预测点对极约束验证失败，Y差异: " + std::to_string(y_diff));
        }
    }
}

cv::Rect StereoReconstructionService::generateROIAroundPoint(const cv::Point2f& center, int size) {
    cv::Size image_size = m_dualEye->imageSize;

    cv::Rect roi(
        static_cast<int>(center.x - size/2),
        static_cast<int>(center.y - size/2),
        size, size
    );

    // 边界裁剪
    int max_x = static_cast<int>(image_size.width) - roi.width;
    int max_y = static_cast<int>(image_size.height) - roi.height;

    if (roi.x < 0) roi.x = 0;
    if (roi.x > max_x) roi.x = max_x;
    if (roi.y < 0) roi.y = 0;
    if (roi.y > max_y) roi.y = max_y;

    return roi;
}