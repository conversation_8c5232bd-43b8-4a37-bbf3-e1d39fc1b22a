# ROI动态可视化功能测试指南

## 测试前准备

### 1. 确认编译成功
确保项目已成功编译，没有错误信息。

### 2. 启动系统
1. 运行 `Camera_Editor.exe`
2. 打开浏览器访问 `http://localhost:8080`
3. 点击"启动系统"按钮

### 3. 检查控制台输出
在启动过程中，应该看到以下关键信息：
```
🎯 ROI预测功能已启用
```

## ROI功能测试步骤

### 第一步：启用ROI可视化
1. 在Web界面的控制面板中找到"动态ROI预测"部分
2. 点击"启用ROI可视化"按钮
3. 按钮应该变为"禁用ROI可视化"，并显示为激活状态

### 第二步：检查ROI状态显示
在ROI状态面板中应该看到：
- **ROI状态**: 已启用
- **立体匹配**: 等待球检测...
- **左摄像头置信度**: --
- **右摄像头置信度**: --

### 第三步：球检测测试
1. 在摄像头视野中放置乒乓球
2. 观察控制台输出，应该看到类似信息：
   ```
   🎯 ROI预测功能已启用
   🎯 左摄像头ROI: (x,y,width x height)
   🎯 右摄像头ROI: (x,y,width x height)
   🎯 立体匹配状态: 可匹配 (左可见: 是, 右可见: 是)
   ```

3. 在Web界面上应该看到：
   - 左摄像头画面上出现绿色ROI框
   - 右摄像头画面上出现蓝色ROI框
   - ROI状态面板显示"立体匹配: 可匹配"
   - 显示左右摄像头的置信度百分比

### 第四步：调整ROI参数
1. **透明度调节**: 使用"ROI透明度"滑块调整ROI框的透明度
2. **置信度阈值**: 调整"置信度阈值"，观察ROI框的显示变化

## 故障排除

### 问题1：ROI框不显示
**可能原因**：
- ROI功能未正确启用
- 球检测失败
- 置信度低于阈值

**解决方法**：
1. 检查控制台是否有"🎯 ROI预测功能已启用"消息
2. 确认球在摄像头视野中且清晰可见
3. 降低置信度阈值到0.1

### 问题2：立体匹配始终显示"不可匹配"
**可能原因**：
- 球只在一个摄像头中可见
- ROI预测功能未正确调用
- 数据传输问题

**调试步骤**：
1. 检查控制台输出中的ROI调试信息
2. 确认球在两个摄像头中都可见
3. 查看是否有"立体匹配状态"的调试输出

### 问题3：ROI框位置不准确
**可能原因**：
- 坐标转换错误
- Canvas尺寸不匹配

**解决方法**：
1. 刷新页面重新初始化Canvas
2. 检查摄像头分辨率设置

## 调试信息

### 控制台调试输出
启用ROI功能后，应该看到以下类型的调试信息：

```
🎯 ROI预测功能已启用
🎯 左摄像头ROI: (123,456,150 x 150)
🎯 右摄像头ROI: (234,567,150 x 150)
🎯 立体匹配状态: 可匹配 (左可见: 是, 右可见: 是)
🎯 已更新StereoROIData - 立体匹配: 是
```

### 浏览器开发者工具
按F12打开开发者工具，在Console标签中应该看到：
```
🎯 设置ROI控制处理器
✅ ROI控制处理器设置完成
📤 发送ROI切换命令: true
✅ ROI切换成功: 启用
```

## 预期结果

### 正常工作状态
1. **ROI框显示**: 左摄像头绿色框，右摄像头蓝色框
2. **置信度标注**: 每个ROI框显示置信度百分比
3. **立体匹配**: 当球在两个摄像头中都可见时显示"可匹配"
4. **实时更新**: ROI框跟随球的移动实时更新

### 性能指标
- ROI框绘制应该流畅，无明显延迟
- 不应影响视频流的播放性能
- 置信度和匹配状态应该实时更新

## 高级测试

### 边界情况测试
1. **单摄像头可见**: 将球移动到只有一个摄像头能看到的位置
2. **快速移动**: 快速移动球，测试ROI跟踪性能
3. **遮挡测试**: 部分遮挡球，观察置信度变化

### 参数调整测试
1. 调整透明度从0.1到1.0，观察视觉效果
2. 调整置信度阈值，观察ROI显示的变化
3. 测试ROI开关的响应性

## 报告问题

如果遇到问题，请提供以下信息：
1. 控制台完整输出
2. 浏览器开发者工具的Console输出
3. ROI状态面板的显示内容
4. 具体的操作步骤和预期vs实际结果

## 下一步改进

基于测试结果，可以考虑以下改进：
1. 优化ROI预测算法的准确性
2. 添加ROI历史轨迹显示
3. 改进置信度计算方法
4. 添加更多的性能监控指标
