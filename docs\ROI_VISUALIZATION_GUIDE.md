# ROI动态可视化功能使用指南

## 功能概述

Camera_Editor项目现已集成动态ROI（Region of Interest）可视化功能，可以在Web前端实时显示双摄像头的ROI预测区域，帮助用户监控和调试AI推理的ROI预测效果。

## 主要特性

### 1. 实时ROI可视化
- **双摄像头支持**：分别显示左右摄像头的ROI预测区域
- **颜色区分**：左摄像头使用绿色（#00ff7f），右摄像头使用蓝色（#00bfff）
- **置信度标注**：实时显示每个ROI的置信度百分比
- **立体匹配状态**：显示双目ROI是否可进行立体匹配

### 2. 用户控制界面
- **启用/禁用开关**：一键切换ROI可视化显示
- **透明度调节**：可调节ROI框的透明度（0.1-1.0）
- **置信度阈值**：设置ROI显示的最低置信度要求（0.1-0.9）
- **实时状态监控**：显示ROI功能状态和性能指标

### 3. 智能显示逻辑
- **置信度过滤**：只显示置信度高于阈值的ROI
- **动态颜色编码**：根据置信度使用不同颜色的内边框
  - 绿色：高置信度（≥0.8）
  - 黄色：中等置信度（≥0.6）
  - 橙色：低置信度（≥0.4）
  - 红色：很低置信度（<0.4）

## 使用方法

### 1. 启动系统
1. 确保Camera_Editor主程序正在运行
2. 打开Web浏览器访问 `http://localhost:8080`
3. 点击"启动系统"按钮启动摄像头和AI推理

### 2. 启用ROI可视化
1. 在控制面板中找到"动态ROI预测"部分
2. 点击"启用ROI可视化"按钮
3. 系统将开始在摄像头画面上叠加显示ROI区域

### 3. 调整显示参数
- **透明度调节**：使用"ROI透明度"滑块调整ROI框的透明度
- **置信度阈值**：使用"置信度阈值"滑块设置显示的最低置信度要求
- **实时监控**：观察ROI状态面板中的实时数据

### 4. 状态监控
ROI状态面板显示以下信息：
- **ROI状态**：显示ROI功能是否已启用
- **立体匹配**：显示双目ROI是否可进行立体匹配
- **左摄像头置信度**：显示左摄像头ROI的置信度
- **右摄像头置信度**：显示右摄像头ROI的置信度

## 技术实现

### 后端实现
- **WebSocket推送**：通过WebSocket实时推送ROI数据
- **SharedData集成**：从SharedData获取最新的StereoROIData
- **消息格式**：JSON格式包含左右ROI坐标、置信度、立体匹配状态

### 前端实现
- **Canvas叠加**：在视频Canvas上叠加ROI Canvas层
- **实时绘制**：根据接收到的ROI数据实时绘制ROI框
- **响应式设计**：自动适应不同的视频分辨率和Canvas尺寸

### 数据流程
1. AI推理服务生成ROI预测数据
2. 数据存储到SharedData的StereoROIData结构
3. WebServerService定期广播ROI数据
4. 前端接收并解析ROI数据
5. 在Canvas上绘制ROI可视化

## 故障排除

### 常见问题

**Q: ROI框不显示**
A: 检查以下项目：
- 确保ROI可视化已启用
- 检查置信度阈值设置是否过高
- 确认AI推理服务正在运行并生成ROI数据

**Q: ROI框位置不准确**
A: 可能的原因：
- Canvas尺寸与实际视频尺寸不匹配
- 坐标转换计算错误
- 需要重新校准摄像头参数

**Q: 性能影响**
A: ROI可视化对性能的影响很小：
- 使用独立的Canvas层，不影响视频渲染
- 只在ROI数据更新时重绘
- 可通过调整透明度减少GPU负载

### 调试建议
1. 打开浏览器开发者工具查看控制台日志
2. 检查WebSocket连接状态
3. 验证ROI数据的JSON格式是否正确
4. 确认SharedData中的ROI数据是否有效

## 扩展功能

### 未来改进方向
- **ROI历史轨迹**：显示ROI区域的移动轨迹
- **性能统计图表**：可视化ROI预测的准确率和效率
- **多目标ROI**：支持同时显示多个目标的ROI
- **ROI编辑功能**：允许用户手动调整ROI区域

### 集成建议
- 与现有的球速检测系统集成
- 结合3D重建功能显示3D ROI
- 与录制系统联动，记录ROI数据
- 添加ROI数据的数据库存储和分析

## 相关文档
- [ROI使用指南](ROI_USAGE_GUIDE.md)
- [动态ROI实现指南](technical/dynamic_roi_implementation_guide.md)
- [AI项目上下文](AI_PROJECT_CONTEXT.md)
