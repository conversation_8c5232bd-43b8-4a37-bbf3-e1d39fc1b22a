#pragma once

#include <mutex>
#include <opencv2/opencv.hpp>
#include <vector> // 包含vector以支持未来的检测结果
#include <map> // 引入 map
#include <string> // 为 std::string 引入
#include <optional>
#include <memory>
#include <chrono> // 添加时间支持
#include <functional> // 添加函数对象支持
#include <atomic> // 新增，支持原子操作
#include "math_utils.hpp" // 引入MU::Point3f
#include "Deploy/yolo.hpp" // 引入Yolo::Detection
#include <opencv2/core/types.hpp> // For cv::Point2f
#include <deque>
#include <queue>

// === 录制控制消息系统 ===

/**
 * @brief 录制命令类型
 */
enum class RecordingCommand {
    START_RECORDING,        // 开始录制
    STOP_RECORDING,         // 停止录制
    START_HIGHLIGHT,        // 开始精彩片段录制
    STOP_HIGHLIGHT,         // 停止精彩片段录制
    PAUSE_RECORDING,        // 暂停录制
    RESUME_RECORDING        // 恢复录制
};

/**
 * @brief 录制控制消息
 */
struct RecordingMessage {
    RecordingCommand command;           // 录制命令
    int camera_id;                     // 目标摄像头ID
    std::string output_path;           // 输出路径（可选）
    std::map<std::string, std::string> parameters; // 额外参数
    std::chrono::steady_clock::time_point timestamp; // 消息时间戳
    std::string request_id;            // 请求ID，用于响应匹配

    RecordingMessage() : command(RecordingCommand::START_RECORDING), camera_id(1),
                        timestamp(std::chrono::steady_clock::now()) {}

    RecordingMessage(RecordingCommand cmd, int cam_id, const std::string& path = "")
        : command(cmd), camera_id(cam_id), output_path(path),
          timestamp(std::chrono::steady_clock::now()) {}
};

/**
 * @brief 录制响应状态
 */
enum class RecordingStatus {
    SUCCESS,                // 成功
    FAILED,                 // 失败
    IN_PROGRESS,           // 进行中
    COMPLETED              // 已完成
};

/**
 * @brief 录制响应消息
 */
struct RecordingResponse {
    std::string request_id;            // 对应的请求ID
    RecordingStatus status;            // 响应状态
    std::string message;               // 状态消息
    std::string output_file;           // 输出文件路径（如果有）
    std::chrono::steady_clock::time_point timestamp; // 响应时间戳

    RecordingResponse() : status(RecordingStatus::SUCCESS),
                         timestamp(std::chrono::steady_clock::now()) {}

    RecordingResponse(const std::string& req_id, RecordingStatus stat, const std::string& msg = "")
        : request_id(req_id), status(stat), message(msg),
          timestamp(std::chrono::steady_clock::now()) {}
};

// 三维坐标数据结构
struct BallPosition3D {
    int id;                           // 球的唯一标识
    MU::Point3f world_position;       // 世界坐标系中的位置
    float confidence;                 // 检测置信度
    std::chrono::milliseconds timestamp; // 时间戳
    
    BallPosition3D() : id(-1), confidence(0.0f), timestamp(0) {}
    
    BallPosition3D(int ball_id, const MU::Point3f& pos, float conf, std::chrono::milliseconds ts)
        : id(ball_id), world_position(pos), confidence(conf), timestamp(ts) {}
};

// 定义一个空的检测结果结构体，为未来的AI推理服务占位
struct DetectionResult {
    std::vector<cv::Rect> boxes;
    std::vector<int> class_ids;
    std::vector<float> confidences;
    std::vector<std::string> class_names;
    // 扩展：添加三维坐标支持
    std::vector<cv::Point3f> world_positions;  // 对应每个检测框的三维坐标
    bool has_3d_data = false;                  // 标识是否包含有效的三维数据
};

class SharedData {
public:
    // === ROI预测数据结构 ===
    struct StereoROIData {
        cv::Rect left_roi;
        cv::Rect right_roi;
        float left_confidence;
        float right_confidence;
        bool stereo_matchable;  // 是否可进行立体匹配
        std::chrono::high_resolution_clock::time_point timestamp;

        StereoROIData() : left_confidence(0.0f), right_confidence(0.0f),
                         stereo_matchable(false) {}
    };

    SharedData() : m_statsStartTime(std::chrono::steady_clock::now()) {}

    // 为指定相机ID设置最新的视频帧
    void setNewFrame(int camera_id, const cv::Mat& frame);

    // 获取指定相机ID的最新视频帧
    cv::Mat getLatestFrame(int camera_id);

    // 为指定相机ID设置最新的检测结果
    void setDetectionResult(int cameraId, const DetectionResult& result);

    // 获取指定相机ID的最新检测结果
    std::optional<DetectionResult> getLatestDetectionResult(int cameraId);

    // === 新增：原始YOLO检测结果存储 ===
    
    // 设置原始YOLO检测结果
    void setRawYoloDetections(int cameraId, const std::map<std::string, std::vector<Yolo::Detection>>& detections);

    // 获取原始YOLO检测结果
    std::optional<std::map<std::string, std::vector<Yolo::Detection>>> getRawYoloDetections(int cameraId);

    // 获取两个摄像头的检测结果用于立体匹配
    std::pair<std::map<std::string, std::vector<Yolo::Detection>>, std::map<std::string, std::vector<Yolo::Detection>>> 
    getStereoDetections(int leftCameraId = 1, int rightCameraId = 2);

    // === 新增：三维坐标数据管理 ===
    
    // 设置最新的三维坐标数据
    void setBallPositions3D(const std::vector<BallPosition3D>& positions);

    // 注册三维坐标更新回调函数
    void setNewBallPositionsCallback(std::function<void()> callback);

    // 通知有新的球位置数据（用于主循环调用）
    void notifyNewBallPositions();

    // 获取最新的三维坐标数据
    std::vector<BallPosition3D> getBallPositions3D() const;

    // 获取最后一次三维数据更新的时间戳
    std::chrono::milliseconds getLast3DUpdateTime() const;

    // 检查是否有有效的三维数据
    bool has3DData() const;

    // 清空三维坐标数据（用于重置或错误恢复）
    void clear3DData();

    // 设置置信度阈值
    void setConfidenceThreshold(float threshold);

    // 获取置信度阈值
    float getConfidenceThreshold();

    void setActualFps(int cameraId, double fps);

    double getActualFps(int cameraId);

    std::map<int, double> getActualFpsMap();

    // === 新增：球速数据管理 ===

    // 设置最新的球速
    void setBallSpeed(double speed);

    // 获取最新的球速
    double getBallSpeed() const;

    // === 新增：批量数据获取（减少锁竞争） ===
    struct DataSnapshot {
        std::vector<BallPosition3D> ball_positions;
        double ball_speed;
        bool has_data;
    };
    DataSnapshot getDataSnapshot() const;

    // === 新增：三维轨迹数据管理 ===

    // 设置最新的轨迹数据
    void setTrajectory(const std::vector<BallPosition3D>& trajectory);

    // 获取最新的轨迹数据
    std::vector<BallPosition3D> getTrajectory() const;

    // 清空轨迹数据
    void clearTrajectory();

    // === ROI预测接口 ===

    /**
     * @brief 设置指定相机的ROI预测
     * @param camera_id 相机ID (1=左, 2=右)
     * @param roi 预测的ROI区域
     * @param confidence 预测置信度 (0.0-1.0)
     */
    void setROIPrediction(int camera_id, const cv::Rect& roi, float confidence);

    /**
     * @brief 获取指定相机的最新ROI预测
     * @param camera_id 相机ID (1=左, 2=右)
     * @return 预测的ROI区域，如果无预测则返回空矩形
     */
    cv::Rect getLatestROI(int camera_id);

    /**
     * @brief 设置双目ROI数据
     * @param stereo_roi 双目ROI数据
     */
    void setStereoROI(const StereoROIData& stereo_roi);

    /**
     * @brief 获取最新的双目ROI数据
     * @return 双目ROI数据
     */
    StereoROIData getLatestStereoROI() const;

    /**
     * @brief 设置立体匹配可用状态
     * @param matchable 是否可进行立体匹配
     */
    void setStereoMatchable(bool matchable);

    /**
     * @brief 检查立体匹配是否可用
     * @return 立体匹配可用状态
     */
    bool isStereoMatchable() const;

    /**
     * @brief 设置ROI处理模式
     * @param enabled 是否启用ROI处理
     */
    void setROIProcessingMode(bool enabled);

    /**
     * @brief 检查ROI处理是否启用
     * @return ROI处理启用状态
     */
    bool isROIProcessingEnabled() const;

    /**
     * @brief 检查指定相机的ROI是否有效
     * @param camera_id 相机ID
     * @return ROI有效性
     */
    bool isROIValid(int camera_id) const;

    // === 录制控制消息系统 ===

    /**
     * @brief 发送录制控制消息
     * @param message 录制控制消息
     */
    void sendRecordingMessage(const RecordingMessage& message);

    /**
     * @brief 获取下一个录制控制消息（非阻塞）
     * @return 录制消息，如果队列为空则返回空optional
     */
    std::optional<RecordingMessage> getNextRecordingMessage();

    /**
     * @brief 发送录制响应消息
     * @param response 录制响应消息
     */
    void sendRecordingResponse(const RecordingResponse& response);

    /**
     * @brief 获取录制响应消息（非阻塞）
     * @param request_id 请求ID，如果为空则获取任意响应
     * @return 录制响应，如果没有匹配的响应则返回空optional
     */
    std::optional<RecordingResponse> getRecordingResponse(const std::string& request_id = "");

    /**
     * @brief 清空录制消息队列
     */
    void clearRecordingMessages();

    /**
     * @brief 获取录制消息队列大小
     */
    size_t getRecordingMessageQueueSize() const;

    // === 系统运行状态控制 ===
    void setSystemRunning(bool is_running);

    bool isSystemRunning() const;

    // --- 2D Trajectory (新增) ---
    void set2dTrajectories(const std::map<int, std::deque<cv::Point2f>>& trajectories);
    std::map<int, std::vector<cv::Point2f>> get2dTrajectories();

    // === 新增：数据流监控统计 ===
    struct DataFlowStatsSnapshot {
        uint64_t total_frames_processed;
        uint64_t total_data_points_logged;
        uint64_t ai_inference_count;
        uint64_t successful_3d_reconstructions;
        std::chrono::steady_clock::time_point start_time;

        double getFrameProcessingRate() const {
            auto elapsed = std::chrono::steady_clock::now() - start_time;
            auto seconds = std::chrono::duration<double>(elapsed).count();
            return seconds > 0 ? total_frames_processed / seconds : 0.0;
        }

        double getDataLoggingRate() const {
            auto elapsed = std::chrono::steady_clock::now() - start_time;
            auto seconds = std::chrono::duration<double>(elapsed).count();
            return seconds > 0 ? total_data_points_logged / seconds : 0.0;
        }
    };

    void incrementFrameCount();
    void incrementDataPointCount();
    void incrementAIInferenceCount();
    void increment3DReconstructionCount();
    DataFlowStatsSnapshot getDataFlowStats() const;

private:
    std::mutex m_framesMutex;
    std::map<int, cv::Mat> m_frames;

    std::mutex m_resultsMutex;
    std::map<int, DetectionResult> m_results;

    // === 新增：原始YOLO检测结果存储 ===
    mutable std::mutex m_rawDetectionsMutex;
    std::map<int, std::map<std::string, std::vector<Yolo::Detection>>> m_rawDetections;

    // For tracking real-time camera performance
    mutable std::mutex m_fpsMutex;
    std::map<int, double> m_actualFps;

    std::mutex m_thresholdMutex;
    float m_confidenceThreshold = 0.4f; // 默认阈值为0.4
    
    // === 新增：三维坐标数据存储 ===
    mutable std::mutex m_3dDataMutex;
    std::vector<BallPosition3D> m_ballPositions3D;
    std::chrono::milliseconds m_last3DUpdate{0};
    std::function<void()> m_3dPositionCallback; // 三维坐标更新回调函数

    // === 新增：球速数据管理 ===
    mutable std::mutex m_speedMutex;
    double m_ballSpeed = 0.0; // 单位：米/秒

    // === 新增：三维轨迹数据管理 ===
    mutable std::mutex m_trajectoryMutex;
    std::vector<BallPosition3D> m_trajectory;

    // === 系统状态 ===
    std::atomic<bool> m_isSystemRunning{false};

    // --- 2D Trajectory (新增) ---
    std::map<int, std::vector<cv::Point2f>> m_trajectories_2d;
    mutable std::mutex m_trajectories_2d_mutex;

    // === 录制控制消息系统 ===
    mutable std::mutex m_recordingMessageMutex;
    std::queue<RecordingMessage> m_recordingMessageQueue;
    std::queue<RecordingResponse> m_recordingResponseQueue;

    // === ROI预测数据 ===
    mutable std::mutex m_roiMutex;
    std::map<int, cv::Rect> m_predictedROIs;        // 每个相机的预测ROI
    std::map<int, float> m_roiConfidences;          // ROI置信度
    StereoROIData m_latestStereoROI;                // 最新的双目ROI数据
    std::atomic<bool> m_roiEnabled{false};          // ROI功能是否启用
    std::atomic<bool> m_stereoMatchable{true};      // 立体匹配是否可用

    // === 数据流监控统计 ===
    mutable std::atomic<uint64_t> m_totalFramesProcessed{0};
    mutable std::atomic<uint64_t> m_totalDataPointsLogged{0};
    mutable std::atomic<uint64_t> m_aiInferenceCount{0};
    mutable std::atomic<uint64_t> m_successful3DReconstructions{0};
    mutable std::chrono::steady_clock::time_point m_statsStartTime;
};

inline void SharedData::setNewFrame(int camera_id, const cv::Mat& frame) {
    std::lock_guard<std::mutex> lock(m_framesMutex);
    m_frames[camera_id] = frame;
}

inline cv::Mat SharedData::getLatestFrame(int camera_id) {
    std::lock_guard<std::mutex> lock(m_framesMutex);
    if (m_frames.count(camera_id)) {
        return m_frames[camera_id];
    }
    return cv::Mat();
}

inline void SharedData::setDetectionResult(int cameraId, const DetectionResult& result) {
    std::lock_guard<std::mutex> lock(m_resultsMutex);
    m_results[cameraId] = result;
}

inline std::optional<DetectionResult> SharedData::getLatestDetectionResult(int cameraId) {
    std::lock_guard<std::mutex> lock(m_resultsMutex);
    if (m_results.count(cameraId)) {
        return m_results.at(cameraId);
    }
    return std::nullopt;
}

inline void SharedData::setRawYoloDetections(int cameraId, const std::map<std::string, std::vector<Yolo::Detection>>& detections) {
    std::lock_guard<std::mutex> lock(m_rawDetectionsMutex);
    m_rawDetections[cameraId] = detections;
}

inline std::optional<std::map<std::string, std::vector<Yolo::Detection>>> SharedData::getRawYoloDetections(int cameraId) {
    std::lock_guard<std::mutex> lock(m_rawDetectionsMutex);
    if (m_rawDetections.count(cameraId)) {
        return m_rawDetections.at(cameraId);
    }
    return std::nullopt;
}

inline std::pair<std::map<std::string, std::vector<Yolo::Detection>>, std::map<std::string, std::vector<Yolo::Detection>>> 
SharedData::getStereoDetections(int leftCameraId, int rightCameraId) {
    std::lock_guard<std::mutex> lock(m_rawDetectionsMutex);
    std::map<std::string, std::vector<Yolo::Detection>> left, right;
    if (m_rawDetections.count(leftCameraId)) {
        left = m_rawDetections[leftCameraId];
    }
    if (m_rawDetections.count(rightCameraId)) {
        right = m_rawDetections[rightCameraId];
    }
    return {left, right};
}

inline void SharedData::setBallPositions3D(const std::vector<BallPosition3D>& positions) {
    std::function<void()> callback_to_run;
    {
        std::lock_guard<std::mutex> lock(m_3dDataMutex);
        m_ballPositions3D = positions;
        m_last3DUpdate = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch());
        
        if (m_3dPositionCallback && !positions.empty()) {
            callback_to_run = m_3dPositionCallback;
        }
    }
    if (callback_to_run) {
        callback_to_run();
    }
}

inline void SharedData::setNewBallPositionsCallback(std::function<void()> callback) {
    std::lock_guard<std::mutex> lock(m_3dDataMutex);
    m_3dPositionCallback = std::move(callback);
}

inline void SharedData::notifyNewBallPositions() {
    std::function<void()> callback_to_run;
    {
        std::lock_guard<std::mutex> lock(m_3dDataMutex);
        if (m_3dPositionCallback) {
            callback_to_run = m_3dPositionCallback;
        }
    }
    if (callback_to_run) {
        callback_to_run();
    }
}

inline std::vector<BallPosition3D> SharedData::getBallPositions3D() const {
    std::lock_guard<std::mutex> lock(m_3dDataMutex);
    return m_ballPositions3D;
}

inline std::chrono::milliseconds SharedData::getLast3DUpdateTime() const {
    std::lock_guard<std::mutex> lock(m_3dDataMutex);
    return m_last3DUpdate;
}

inline bool SharedData::has3DData() const {
    std::lock_guard<std::mutex> lock(m_3dDataMutex);
    return !m_ballPositions3D.empty();
}

inline void SharedData::clear3DData() {
    std::lock_guard<std::mutex> lock(m_3dDataMutex);
    m_ballPositions3D.clear();
}

inline void SharedData::setConfidenceThreshold(float threshold) {
    std::lock_guard<std::mutex> lock(m_thresholdMutex);
    m_confidenceThreshold = threshold;
}

inline float SharedData::getConfidenceThreshold() {
    std::lock_guard<std::mutex> lock(m_thresholdMutex);
    return m_confidenceThreshold;
}

inline void SharedData::setActualFps(int cameraId, double fps) {
    std::lock_guard<std::mutex> lock(m_fpsMutex);
    m_actualFps[cameraId] = fps;
}

inline double SharedData::getActualFps(int cameraId) {
    std::lock_guard<std::mutex> lock(m_fpsMutex);
    if (m_actualFps.count(cameraId)) {
        return m_actualFps[cameraId];
    }
    return 0.0;
}

inline std::map<int, double> SharedData::getActualFpsMap() {
    std::lock_guard<std::mutex> lock(m_fpsMutex);
    return m_actualFps;
}

inline void SharedData::setBallSpeed(double speed) {
    std::lock_guard<std::mutex> lock(m_speedMutex);
    m_ballSpeed = speed;
}

inline double SharedData::getBallSpeed() const {
    std::lock_guard<std::mutex> lock(m_speedMutex);
    return m_ballSpeed;
}

inline void SharedData::setTrajectory(const std::vector<BallPosition3D>& trajectory) {
    std::lock_guard<std::mutex> lock(m_trajectoryMutex);
    m_trajectory = trajectory;
}

inline std::vector<BallPosition3D> SharedData::getTrajectory() const {
    std::lock_guard<std::mutex> lock(m_trajectoryMutex);
    return m_trajectory;
}

inline void SharedData::clearTrajectory() {
    std::lock_guard<std::mutex> lock(m_trajectoryMutex);
    m_trajectory.clear();
}

inline void SharedData::setSystemRunning(bool is_running) {
    m_isSystemRunning.store(is_running);
}

inline bool SharedData::isSystemRunning() const {
    return m_isSystemRunning.load();
}

inline void SharedData::set2dTrajectories(const std::map<int, std::deque<cv::Point2f>>& trajectories) {
    std::lock_guard<std::mutex> lock(m_trajectories_2d_mutex);
    m_trajectories_2d.clear();
    for (const auto& pair : trajectories) {
        m_trajectories_2d[pair.first] = std::vector<cv::Point2f>(pair.second.begin(), pair.second.end());
    }
}

inline std::map<int, std::vector<cv::Point2f>> SharedData::get2dTrajectories() {
    std::lock_guard<std::mutex> lock(m_trajectories_2d_mutex);
    return m_trajectories_2d;
}

inline SharedData::DataSnapshot SharedData::getDataSnapshot() const {
    DataSnapshot snapshot;

    // 使用两个锁来获取3D数据和速度数据
    // 注意：这里仍然需要两个锁，但减少了调用次数
    {
        std::lock_guard<std::mutex> lock(m_3dDataMutex);
        snapshot.ball_positions = m_ballPositions3D;
        snapshot.has_data = !m_ballPositions3D.empty();
    }

    {
        std::lock_guard<std::mutex> lock(m_speedMutex);
        snapshot.ball_speed = m_ballSpeed;
    }

    return snapshot;
}

inline void SharedData::incrementFrameCount() {
    m_totalFramesProcessed++;
}

inline void SharedData::incrementDataPointCount() {
    m_totalDataPointsLogged++;
}

inline void SharedData::incrementAIInferenceCount() {
    m_aiInferenceCount++;
}

inline void SharedData::increment3DReconstructionCount() {
    m_successful3DReconstructions++;
}

inline SharedData::DataFlowStatsSnapshot SharedData::getDataFlowStats() const {
    DataFlowStatsSnapshot snapshot;
    snapshot.total_frames_processed = m_totalFramesProcessed.load();
    snapshot.total_data_points_logged = m_totalDataPointsLogged.load();
    snapshot.ai_inference_count = m_aiInferenceCount.load();
    snapshot.successful_3d_reconstructions = m_successful3DReconstructions.load();
    snapshot.start_time = m_statsStartTime;
    return snapshot;
}

// === 录制控制消息系统实现 ===

inline void SharedData::sendRecordingMessage(const RecordingMessage& message) {
    std::lock_guard<std::mutex> lock(m_recordingMessageMutex);
    m_recordingMessageQueue.push(message);
}

inline std::optional<RecordingMessage> SharedData::getNextRecordingMessage() {
    std::lock_guard<std::mutex> lock(m_recordingMessageMutex);
    if (m_recordingMessageQueue.empty()) {
        return std::nullopt;
    }
    RecordingMessage message = m_recordingMessageQueue.front();
    m_recordingMessageQueue.pop();
    return message;
}

inline void SharedData::sendRecordingResponse(const RecordingResponse& response) {
    std::lock_guard<std::mutex> lock(m_recordingMessageMutex);
    m_recordingResponseQueue.push(response);
}

inline std::optional<RecordingResponse> SharedData::getRecordingResponse(const std::string& request_id) {
    std::lock_guard<std::mutex> lock(m_recordingMessageMutex);

    if (m_recordingResponseQueue.empty()) {
        return std::nullopt;
    }

    // 如果没有指定request_id，返回队列中的第一个响应
    if (request_id.empty()) {
        RecordingResponse response = m_recordingResponseQueue.front();
        m_recordingResponseQueue.pop();
        return response;
    }

    // 查找匹配的request_id（需要遍历队列）
    std::queue<RecordingResponse> temp_queue;
    std::optional<RecordingResponse> found_response;

    while (!m_recordingResponseQueue.empty()) {
        RecordingResponse response = m_recordingResponseQueue.front();
        m_recordingResponseQueue.pop();

        if (response.request_id == request_id && !found_response.has_value()) {
            found_response = response;
        } else {
            temp_queue.push(response);
        }
    }

    // 将未匹配的响应放回队列
    m_recordingResponseQueue = temp_queue;

    return found_response;
}

inline void SharedData::clearRecordingMessages() {
    std::lock_guard<std::mutex> lock(m_recordingMessageMutex);
    while (!m_recordingMessageQueue.empty()) {
        m_recordingMessageQueue.pop();
    }
    while (!m_recordingResponseQueue.empty()) {
        m_recordingResponseQueue.pop();
    }
}

inline size_t SharedData::getRecordingMessageQueueSize() const {
    std::lock_guard<std::mutex> lock(m_recordingMessageMutex);
    return m_recordingMessageQueue.size();
}

// === ROI预测接口实现 ===

inline void SharedData::setROIPrediction(int camera_id, const cv::Rect& roi, float confidence) {
    std::lock_guard<std::mutex> lock(m_roiMutex);
    m_predictedROIs[camera_id] = roi;
    m_roiConfidences[camera_id] = confidence;
}

inline cv::Rect SharedData::getLatestROI(int camera_id) {
    std::lock_guard<std::mutex> lock(m_roiMutex);
    if (m_predictedROIs.count(camera_id)) {
        return m_predictedROIs[camera_id];
    }
    return cv::Rect();  // 返回空矩形
}

inline void SharedData::setStereoROI(const StereoROIData& stereo_roi) {
    std::lock_guard<std::mutex> lock(m_roiMutex);
    m_latestStereoROI = stereo_roi;
}

inline SharedData::StereoROIData SharedData::getLatestStereoROI() const {
    std::lock_guard<std::mutex> lock(m_roiMutex);
    return m_latestStereoROI;
}

inline void SharedData::setStereoMatchable(bool matchable) {
    m_stereoMatchable.store(matchable);
}

inline bool SharedData::isStereoMatchable() const {
    return m_stereoMatchable.load();
}

inline void SharedData::setROIProcessingMode(bool enabled) {
    m_roiEnabled.store(enabled);
}

inline bool SharedData::isROIProcessingEnabled() const {
    return m_roiEnabled.load();
}

inline bool SharedData::isROIValid(int camera_id) const {
    std::lock_guard<std::mutex> lock(m_roiMutex);
    if (m_predictedROIs.count(camera_id)) {
        const cv::Rect& roi = m_predictedROIs.at(camera_id);
        return roi.area() > 100 && roi.x >= 0 && roi.y >= 0;
    }
    return false;
}