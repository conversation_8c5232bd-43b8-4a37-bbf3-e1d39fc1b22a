#include "DebugConfig.hpp"
#include <iomanip>

// === 静态成员变量初始化 ===

// 模块调试开关 - 默认配置（根据实际需要调整）
bool DebugConfig::enable_ball_speed_debug = false;        // 球速调试默认关闭
bool DebugConfig::enable_camera_sync_debug = false;       // 摄像头同步调试默认关闭
bool DebugConfig::enable_frame_detection_debug = false;   // 帧检测调试默认关闭
bool DebugConfig::enable_highlight_debug = true;          // 精彩时刻调试默认开启
bool DebugConfig::enable_3d_reconstruction_debug = false; // 3D重建调试默认关闭
bool DebugConfig::enable_recording_debug = true;          // 录制功能调试默认开启
bool DebugConfig::enable_web_server_debug = false;        // Web服务调试默认关闭
bool DebugConfig::enable_roi_debug = true;                // ROI预测调试默认开启

// 日志级别控制 - 默认INFO级别
DebugConfig::LogLevel DebugConfig::current_log_level = DebugConfig::INFO_LEVEL;

// 摘要输出控制 - 默认启用摘要模式
bool DebugConfig::enable_summary_mode = true;
int DebugConfig::summary_interval_seconds = 5;

// === 函数实现 ===

void DebugConfig::toggleBallSpeedDebug() {
    enable_ball_speed_debug = !enable_ball_speed_debug;
    logConfigChange("球速调试", enable_ball_speed_debug);
}

void DebugConfig::toggleCameraSyncDebug() {
    enable_camera_sync_debug = !enable_camera_sync_debug;
    logConfigChange("摄像头同步调试", enable_camera_sync_debug);
}

void DebugConfig::toggleFrameDetectionDebug() {
    enable_frame_detection_debug = !enable_frame_detection_debug;
    logConfigChange("帧检测调试", enable_frame_detection_debug);
}

void DebugConfig::toggleHighlightDebug() {
    enable_highlight_debug = !enable_highlight_debug;
    logConfigChange("精彩时刻调试", enable_highlight_debug);
}

void DebugConfig::setLogLevel(LogLevel level) {
    current_log_level = level;
    std::cout << "🔧 日志级别已设置为: " << logLevelToString(level) << std::endl;
}

void DebugConfig::setSummaryMode(bool enabled, int interval_seconds) {
    enable_summary_mode = enabled;
    summary_interval_seconds = interval_seconds;
    std::cout << "📊 摘要模式: " << (enabled ? "启用" : "禁用")
              << (enabled ? " (间隔: " + std::to_string(interval_seconds) + "秒)" : "") << std::endl;
}

void DebugConfig::enableAllDebug() {
    enable_ball_speed_debug = true;
    enable_camera_sync_debug = true;
    enable_frame_detection_debug = true;
    enable_highlight_debug = true;
    enable_3d_reconstruction_debug = true;
    enable_recording_debug = true;
    enable_web_server_debug = true;
    std::cout << "🔓 所有调试模块已启用" << std::endl;
}

void DebugConfig::disableAllDebug() {
    enable_ball_speed_debug = false;
    enable_camera_sync_debug = false;
    enable_frame_detection_debug = false;
    enable_highlight_debug = false;
    enable_3d_reconstruction_debug = false;
    enable_recording_debug = false;
    enable_web_server_debug = false;
    std::cout << "🔒 所有调试模块已禁用" << std::endl;
}

void DebugConfig::showCurrentConfig() {
    std::cout << "\n=== 🔧 调试配置状态 ===" << std::endl;
    std::cout << "球速计算调试: " << (enable_ball_speed_debug ? "✅" : "❌") << std::endl;
    std::cout << "摄像头同步调试: " << (enable_camera_sync_debug ? "✅" : "❌") << std::endl;
    std::cout << "帧检测调试: " << (enable_frame_detection_debug ? "✅" : "❌") << std::endl;
    std::cout << "精彩时刻调试: " << (enable_highlight_debug ? "✅" : "❌") << std::endl;
    std::cout << "3D重建调试: " << (enable_3d_reconstruction_debug ? "✅" : "❌") << std::endl;
    std::cout << "录制功能调试: " << (enable_recording_debug ? "✅" : "❌") << std::endl;
    std::cout << "Web服务调试: " << (enable_web_server_debug ? "✅" : "❌") << std::endl;
    std::cout << "日志级别: " << logLevelToString(current_log_level) << std::endl;
    std::cout << "摘要模式: " << (enable_summary_mode ? "✅ (" + std::to_string(summary_interval_seconds) + "秒)" : "❌") << std::endl;
    std::cout << "========================\n" << std::endl;
}

bool DebugConfig::shouldLog(LogLevel level) {
    return level >= current_log_level;
}

std::string DebugConfig::getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

void DebugConfig::logConfigChange(const std::string& module, bool enabled) {
    std::cout << "🔧 " << module << ": " << (enabled ? "启用" : "禁用") << std::endl;
}

std::string DebugConfig::logLevelToString(LogLevel level) {
    switch (level) {
        case DEBUG_LEVEL: return "DEBUG";
        case INFO_LEVEL: return "INFO";
        case WARNING_LEVEL: return "WARNING";
        case ERROR_LEVEL: return "ERROR";
        case CRITICAL_LEVEL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

/**
 * @brief 初始化调试配置的便捷函数
 * 
 * 可以在main函数中调用，根据不同的运行模式设置不同的调试配置
 */
namespace DebugConfigPresets {
    
    /**
     * @brief 开发调试模式 - 启用大部分调试信息
     */
    void setDevelopmentMode() {
        DebugConfig::enable_ball_speed_debug = true;
        DebugConfig::enable_camera_sync_debug = true;
        DebugConfig::enable_frame_detection_debug = false; // 帧检测信息太多，默认关闭
        DebugConfig::enable_highlight_debug = true;
        DebugConfig::enable_3d_reconstruction_debug = false; // 3D重建信息太多，默认关闭
        DebugConfig::enable_recording_debug = true;
        DebugConfig::enable_web_server_debug = false;
        DebugConfig::current_log_level = DebugConfig::DEBUG_LEVEL;
        DebugConfig::enable_summary_mode = false; // 开发模式下禁用摘要，显示详细信息
        
        std::cout << "🔧 调试配置: 开发模式已启用" << std::endl;
    }
    
    /**
     * @brief 生产运行模式 - 只显示关键信息
     */
    void setProductionMode() {
        DebugConfig::disableAllDebug();
        DebugConfig::current_log_level = DebugConfig::WARNING_LEVEL;
        DebugConfig::enable_summary_mode = true;
        DebugConfig::summary_interval_seconds = 10;
        
        std::cout << "🔧 调试配置: 生产模式已启用" << std::endl;
    }
    
    /**
     * @brief 球速调试专用模式 - 专注于球速计算问题
     */
    void setBallSpeedDebugMode() {
        DebugConfig::disableAllDebug();
        DebugConfig::enable_ball_speed_debug = true;
        DebugConfig::enable_camera_sync_debug = true; // 同步问题影响球速
        DebugConfig::enable_3d_reconstruction_debug = true; // 3D重建影响球速
        DebugConfig::current_log_level = DebugConfig::DEBUG_LEVEL;
        DebugConfig::enable_summary_mode = false;

        std::cout << "🔧 调试配置: 球速调试模式已启用" << std::endl;
    }

    /**
     * @brief 录制调试专用模式 - 专注于录制和精彩片段问题
     */
    void setRecordingDebugMode() {
        DebugConfig::disableAllDebug();
        DebugConfig::enable_recording_debug = true;
        DebugConfig::enable_highlight_debug = true;
        DebugConfig::enable_ball_speed_debug = true; // 球速影响精彩片段检测
        DebugConfig::current_log_level = DebugConfig::DEBUG_LEVEL;
        DebugConfig::enable_summary_mode = false;

        std::cout << "🔧 调试配置: 录制调试模式已启用" << std::endl;
    }

    /**
     * @brief 摘要模式 - 只显示定期摘要信息
     */
    void setSummaryOnlyMode() {
        DebugConfig::disableAllDebug();
        DebugConfig::current_log_level = DebugConfig::INFO_LEVEL;
        DebugConfig::enable_summary_mode = true;
        DebugConfig::summary_interval_seconds = 3;
        
        std::cout << "🔧 调试配置: 摘要模式已启用" << std::endl;
    }
}

/**
 * @brief 摘要信息管理类
 * 
 * 用于收集和定期输出系统关键信息摘要
 */
class SystemSummary {
private:
    static std::chrono::steady_clock::time_point last_summary_time;
    static double last_ball_speed;
    static int frame_count;
    static int detection_count;
    
public:
    /**
     * @brief 更新球速信息
     */
    static void updateBallSpeed(double speed) {
        last_ball_speed = speed;
    }
    
    /**
     * @brief 增加帧计数
     */
    static void incrementFrameCount() {
        frame_count++;
    }
    
    /**
     * @brief 增加检测计数
     */
    static void incrementDetectionCount() {
        detection_count++;
    }
    
    /**
     * @brief 检查是否应该输出摘要，如果是则输出
     */
    static void checkAndOutputSummary() {
        if (!DebugConfig::enable_summary_mode) return;
        
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_summary_time).count();
        
        if (elapsed >= DebugConfig::summary_interval_seconds) {
            outputSummary();
            last_summary_time = now;
            
            // 重置计数器
            frame_count = 0;
            detection_count = 0;
        }
    }
    
private:
    /**
     * @brief 输出系统摘要信息
     */
    static void outputSummary() {
        auto timestamp = DebugConfig::getCurrentTimestamp();
        double fps = static_cast<double>(frame_count) / DebugConfig::summary_interval_seconds;
        double detection_rate = frame_count > 0 ? (static_cast<double>(detection_count) / frame_count * 100) : 0;
        
        std::cout << "\n📊 [" << timestamp << "] 系统摘要 (" << DebugConfig::summary_interval_seconds << "秒)" << std::endl;
        std::cout << "   球速: " << std::fixed << std::setprecision(2) << last_ball_speed << " m/s" << std::endl;
        std::cout << "   处理帧率: " << std::fixed << std::setprecision(1) << fps << " FPS" << std::endl;
        std::cout << "   检测成功率: " << std::fixed << std::setprecision(1) << detection_rate << "%" << std::endl;
        std::cout << "   总帧数: " << frame_count << ", 检测数: " << detection_count << std::endl;
        std::cout << "----------------------------------------" << std::endl;
    }
};

// 静态成员初始化
std::chrono::steady_clock::time_point SystemSummary::last_summary_time = std::chrono::steady_clock::now();
double SystemSummary::last_ball_speed = 0.0;
int SystemSummary::frame_count = 0;
int SystemSummary::detection_count = 0;
