#include "RecordingService.hpp"
#include <iostream>
#include <chrono>
#include <sstream>
#include <iomanip>
#include <vector>
#include <filesystem>
#include <mutex>
#include <ctime>
#include <limits>

#include "../Utils/utf8_utils.hpp"

#ifdef _WIN32
#define POPEN _popen
#define PCLOSE _pclose
#else
#define POPEN popen
#define PCLOSE pclose
#endif

// Helper to create a timestamped filename
std::string RecordingService::create_timestamped_filename(int camera_id, const std::string& base_path) {
    auto now = std::chrono::system_clock::now();
    auto in_time_t = std::chrono::system_clock::to_time_t(now);
    std::tm buf{};
#ifdef _WIN32
    localtime_s(&buf, &in_time_t);
#else
    localtime_r(&in_time_t, &buf); // POSIX
#endif
    std::stringstream ss;
    ss << "cam_" << camera_id << "_"
       << std::put_time(&buf, "%Y%m%d_%H%M%S") << ".mp4";
    
    // Create an absolute path for the output file to avoid ambiguity.
    std::filesystem::path output_dir = base_path;
    std::filesystem::path full_path = output_dir / ss.str();
    
    // Revert to native path format (.string()) to ensure consistent path separators (backslashes on Windows)
    // in the final command, which is more robust for cmd.exe.
    return full_path.string();
}

RecordingService::RecordingService(const std::string& recordings_path, std::shared_ptr<SharedData> shared_data)
    : recordings_path_(recordings_path), shared_data_(shared_data) {
    // Ensure the recordings directory exists.
    if (!std::filesystem::exists(recordings_path_)) {
        std::filesystem::create_directory(recordings_path_);
        UTF8Utils::println("Created recordings directory at: " + recordings_path_);
    }

    // 如果提供了SharedData，启动消息监听
    if (shared_data_) {
        startMessageListener();
    }
}

RecordingService::~RecordingService() {
    // 停止消息监听
    stopMessageListener();

    std::vector<int> active_ids;
    {
        std::lock_guard<std::mutex> lock(service_mutex_);
        for (auto const& [id, is_active] : recording_active_) {
            if (is_active) {
                active_ids.push_back(id);
            }
        }
    }
    for (int id : active_ids) {
        stopRecording(id);
    }
}

void RecordingService::pushFrame(int camera_id, const cv::Mat& frame) {
    std::lock_guard<std::mutex> lock(service_mutex_);
    // Store frame size if not already stored
    if (!frame.empty() && frame_sizes_.find(camera_id) == frame_sizes_.end()) {
        frame_sizes_[camera_id] = frame.size();
    }

    if (image_queues_.count(camera_id)) {
        // 创建带时间戳的帧数据
        TimestampedFrame timestamped_frame;
        timestamped_frame.frame = frame.clone();
        timestamped_frame.timestamp = std::chrono::steady_clock::now();
        image_queues_[camera_id]->push(timestamped_frame);
    }
}

void RecordingService::startRecording(int camera_id) {
    std::lock_guard<std::mutex> lock(service_mutex_);
    if (recording_active_.count(camera_id) && recording_active_[camera_id]) {
        return;
    }

    if (frame_sizes_.find(camera_id) == frame_sizes_.end()) {
        std::cerr << "ERROR: Cannot start recording for camera " << camera_id
                  << ". Frame dimensions not known. Push at least one frame first." << std::endl;
        return;
    }

    image_queues_[camera_id] = std::make_unique<ThreadSafeQueue<TimestampedFrame>>();
    recording_active_[camera_id] = true;
    worker_threads_[camera_id] = std::thread(&RecordingService::worker, this, camera_id);
}

void RecordingService::stopRecording(int camera_id) {
    {
        std::lock_guard<std::mutex> lock(service_mutex_);
        if (!recording_active_.count(camera_id) || !recording_active_[camera_id]) {
            return;
        }
        recording_active_[camera_id] = false;
    }

    if (worker_threads_.count(camera_id) && worker_threads_[camera_id].joinable()) {
        worker_threads_[camera_id].join();
    }

    std::lock_guard<std::mutex> lock(service_mutex_);
    worker_threads_.erase(camera_id);
    image_queues_.erase(camera_id);
    recording_active_.erase(camera_id);
}

void RecordingService::startAll() {
    // Assuming camera IDs are 1 and 2.
    // This could be made more dynamic if needed.
    for (int id : {1, 2}) {
        startRecording(id);
    }
}

void RecordingService::stopAll() {
    for (int id : {1, 2}) {
        stopRecording(id);
    }
}

bool RecordingService::isRecording(int camera_id) {
    std::lock_guard<std::mutex> lock(service_mutex_);
    return recording_active_.count(camera_id) && recording_active_[camera_id];
}

std::map<int, bool> RecordingService::getRecordingStatus() {
    std::lock_guard<std::mutex> lock(service_mutex_);
    return recording_active_;
}

void RecordingService::worker(int camera_id) {
    cv::Size frame_size;
    {
        std::lock_guard<std::mutex> lock(service_mutex_);
        if (frame_sizes_.find(camera_id) == frame_sizes_.end()) {
            std::cerr << "Worker thread for cam " << camera_id << " exiting: frame size unknown." << std::endl;
            return;
        }
        frame_size = frame_sizes_.at(camera_id);
    }
    int width = frame_size.width;
    int height = frame_size.height;

    std::string output_path = create_timestamped_filename(camera_id, recordings_path_);

    // Using a hardcoded absolute path is brittle. It's better to assume ffmpeg is in the system's PATH.
    std::string ffmpeg_executable = "ffmpeg";

    // 优化的FFmpeg命令，针对210 FPS高帧率录制
    std::ostringstream command_stream;
    command_stream << ffmpeg_executable << " -y -f rawvideo -vcodec rawvideo -pix_fmt bgr24 -s "
                   << width << "x" << height << " -r " << TARGET_FPS
                   << " -i - -c:v h264_nvenc -preset p1 -tune ll -rc vbr -cq 23 "
                   << "-b:v 50M -maxrate 100M -bufsize 200M -vf \"format=yuv420p\" "
                   << "-pix_fmt yuv420p -threads 0 \"" << output_path << "\"";
    std::string command = command_stream.str();

    // FFmpeg command prepared for camera recording

    FILE* pipe = POPEN(command.c_str(), "wb");
    if (!pipe) {
        std::cerr << "ERROR: Cannot open ffmpeg pipe for camera " << camera_id << std::endl;
        // No need to set active to false here, stopRecording will handle cleanup.
        return;
    }

    TimestampedFrame last_valid_frame;
    std::chrono::steady_clock::time_point last_written_timestamp;
    std::chrono::steady_clock::time_point recording_start_time;
    int frames_written = 0;

    // Wait for the very first frame to arrive to initialize last_valid_frame
    image_queues_[camera_id]->wait_and_pop(last_valid_frame);
    last_written_timestamp = last_valid_frame.timestamp;
    recording_start_time = std::chrono::steady_clock::now();

    // 写入第一帧
    if (!last_valid_frame.frame.empty()) {
        fwrite(last_valid_frame.frame.data, 1,
               last_valid_frame.frame.total() * last_valid_frame.frame.elemSize(), pipe);
        frames_written++;
    }

    while (true) {
        bool should_stop = false;
        {
            std::lock_guard<std::mutex> lock(service_mutex_);
            should_stop = !recording_active_[camera_id];
        }
        if(should_stop) break;

        // 等待新帧到达，使用阻塞等待避免CPU空转
        TimestampedFrame new_frame;
        if (!image_queues_[camera_id]->wait_and_pop_timeout(new_frame, std::chrono::milliseconds(50))) {
            // 超时，检查是否需要停止
            continue;
        }

        // 只处理时间戳更新的帧，避免重复帧
        if (new_frame.timestamp <= last_written_timestamp) {
            continue; // 跳过旧帧或重复帧
        }

        // 写入新帧
        if (!new_frame.frame.empty()) {
            fwrite(new_frame.frame.data, 1,
                   new_frame.frame.total() * new_frame.frame.elemSize(), pipe);
            frames_written++;
            last_written_timestamp = new_frame.timestamp;
        }

        // Frame recording progress (silent mode)
    }

    fflush(pipe);
    PCLOSE(pipe);
}

void RecordingService::enableStereoSync(bool enable) {
    stereo_sync_enabled_.store(enable);
}

bool RecordingService::isStereoSyncEnabled() const {
    return stereo_sync_enabled_.load();
}

// === 新增：消息驱动录制控制实现 ===

void RecordingService::startMessageListener() {
    if (!shared_data_ || message_listener_running_.load()) {
        return;
    }

    message_listener_running_.store(true);
    message_listener_thread_ = std::make_unique<std::thread>(&RecordingService::messageListenerThread, this);
    UTF8Utils::println("[RecordingService] Message listener started");
}

void RecordingService::stopMessageListener() {
    if (!message_listener_running_.load()) {
        return;
    }

    message_listener_running_.store(false);
    if (message_listener_thread_ && message_listener_thread_->joinable()) {
        message_listener_thread_->join();
    }
    message_listener_thread_.reset();
    UTF8Utils::println("[RecordingService] Message listener stopped");
}

void RecordingService::messageListenerThread() {
    UTF8Utils::println("[RecordingService] Message listener thread started");

    while (message_listener_running_.load()) {
        if (!shared_data_) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        }

        // 检查是否有新的录制消息
        auto message = shared_data_->getNextRecordingMessage();
        if (message.has_value()) {
            processRecordingMessage(message.value());
        }

        // 避免CPU空转
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }

    UTF8Utils::println("[RecordingService] Message listener thread ended");
}

void RecordingService::processRecordingMessage(const RecordingMessage& message) {
    UTF8Utils::println("[RecordingService] Processing recording message: command=" +
                      std::to_string(static_cast<int>(message.command)) +
                      ", camera_id=" + std::to_string(message.camera_id));

    try {
        switch (message.command) {
            case RecordingCommand::START_RECORDING:
                startRecording(message.camera_id);
                sendRecordingResponse(message.request_id, RecordingStatus::SUCCESS, "Recording started");
                break;

            case RecordingCommand::STOP_RECORDING:
                stopRecording(message.camera_id);
                sendRecordingResponse(message.request_id, RecordingStatus::SUCCESS, "Recording stopped");
                break;

            case RecordingCommand::START_HIGHLIGHT:
                if (startHighlightRecording(message.camera_id, message.output_path, message.request_id)) {
                    sendRecordingResponse(message.request_id, RecordingStatus::IN_PROGRESS, "Highlight recording started");
                } else {
                    sendRecordingResponse(message.request_id, RecordingStatus::FAILED, "Failed to start highlight recording");
                }
                break;

            case RecordingCommand::STOP_HIGHLIGHT: {
                std::string output_file = stopHighlightRecording(message.camera_id, message.request_id);
                if (!output_file.empty()) {
                    sendRecordingResponse(message.request_id, RecordingStatus::COMPLETED, "Highlight recording completed", output_file);
                } else {
                    sendRecordingResponse(message.request_id, RecordingStatus::FAILED, "Failed to stop highlight recording");
                }
                break;
            }

            case RecordingCommand::PAUSE_RECORDING:
                // TODO: 实现暂停功能
                sendRecordingResponse(message.request_id, RecordingStatus::FAILED, "Pause not implemented yet");
                break;

            case RecordingCommand::RESUME_RECORDING:
                // TODO: 实现恢复功能
                sendRecordingResponse(message.request_id, RecordingStatus::FAILED, "Resume not implemented yet");
                break;

            default:
                sendRecordingResponse(message.request_id, RecordingStatus::FAILED, "Unknown recording command");
                break;
        }
    } catch (const std::exception& e) {
        sendRecordingResponse(message.request_id, RecordingStatus::FAILED,
                            "Exception processing recording message: " + std::string(e.what()));
    }
}

void RecordingService::sendRecordingResponse(const std::string& request_id, RecordingStatus status,
                                           const std::string& message, const std::string& output_file) {
    if (!shared_data_) {
        return;
    }

    RecordingResponse response(request_id, status, message);
    response.output_file = output_file;
    shared_data_->sendRecordingResponse(response);
}

bool RecordingService::startHighlightRecording(int camera_id, const std::string& output_path, const std::string& request_id) {
    std::lock_guard<std::mutex> lock(service_mutex_);

    // 检查是否已经在录制
    if (recording_active_.count(camera_id) && recording_active_[camera_id]) {
        UTF8Utils::println("[RecordingService] Camera " + std::to_string(camera_id) + " is already recording");
        return false;
    }

    // 存储精彩片段录制信息
    highlight_recording_paths_[camera_id] = output_path;
    highlight_request_ids_[camera_id] = request_id;

    // 内联启动录制逻辑（避免重复加锁）
    if (frame_sizes_.find(camera_id) == frame_sizes_.end()) {
        UTF8Utils::println("[RecordingService] ERROR: Cannot start recording for camera " + std::to_string(camera_id) +
                          ". Frame dimensions not known. Push at least one frame first.");
        return false;
    }

    image_queues_[camera_id] = std::make_unique<ThreadSafeQueue<TimestampedFrame>>();
    recording_active_[camera_id] = true;
    worker_threads_[camera_id] = std::thread(&RecordingService::worker, this, camera_id);

    UTF8Utils::println("[RecordingService] Started highlight recording for camera " + std::to_string(camera_id) +
                      " to " + output_path);
    return true;
}

std::string RecordingService::stopHighlightRecording(int camera_id, const std::string& request_id) {
    std::lock_guard<std::mutex> lock(service_mutex_);

    // 检查是否有对应的精彩片段录制
    if (highlight_request_ids_.find(camera_id) == highlight_request_ids_.end() ||
        highlight_request_ids_[camera_id] != request_id) {
        UTF8Utils::println("[RecordingService] No matching highlight recording found for camera " +
                          std::to_string(camera_id) + " with request_id " + request_id);
        return "";
    }

    // 内联停止录制逻辑（避免重复加锁）
    if (recording_active_.count(camera_id) && recording_active_[camera_id]) {
        recording_active_[camera_id] = false;
        if (worker_threads_.count(camera_id) && worker_threads_[camera_id].joinable()) {
            worker_threads_[camera_id].join();
        }
        worker_threads_.erase(camera_id);
        image_queues_.erase(camera_id);
    }

    // 获取输出路径
    std::string output_path = highlight_recording_paths_[camera_id];

    // 清理记录
    highlight_recording_paths_.erase(camera_id);
    highlight_request_ids_.erase(camera_id);

    UTF8Utils::println("[RecordingService] Stopped highlight recording for camera " + std::to_string(camera_id) +
                      ", output: " + output_path);

    return output_path;
}