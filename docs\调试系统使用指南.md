# 🔧 Camera_Editor调试系统使用指南

## 概述

为了解决终端信息过载的问题，我们实现了一个模块化的调试系统，支持：
- **模块化调试开关** - 按功能模块控制调试输出
- **日志级别分类** - DEBUG/INFO/WARNING/ERROR/CRITICAL
- **预设调试模式** - 针对不同调试场景的快速配置
- **摘要输出模式** - 定期输出关键信息摘要

## 🚀 快速开始

### 1. 选择调试模式

在 `Main/main.cpp` 中，取消注释相应的调试模式：

```cpp
// 初始化调试配置 - 根据需要选择不同的模式
// DebugConfigPresets::setDevelopmentMode();     // 开发调试模式
// DebugConfigPresets::setBallSpeedDebugMode();  // 球速调试专用模式
// DebugConfigPresets::setRecordingDebugMode();  // 录制调试专用模式
DebugConfigPresets::setSummaryOnlyMode();        // 摘要模式（推荐）
// DebugConfigPresets::setProductionMode();      // 生产模式
```

### 2. 编译运行

```bash
# 编译项目
cmake --build . --config Release

# 运行程序
./Release/Camera_Editor.exe
```

## 📊 调试模式说明

### 🔍 摘要模式（推荐）
**适用场景**: 日常使用，关注系统整体状态

**特点**:
- 每3秒输出一次系统摘要
- 显示球速、处理帧率、检测成功率
- 只显示WARNING及以上级别的日志
- 终端信息简洁清晰

**输出示例**:
```
📊 [14:23:45.123] 系统摘要 (3秒)
   球速: 12.34 m/s
   处理帧率: 208.5 FPS
   检测成功率: 85.2%
   总帧数: 625, 检测数: 533
----------------------------------------
```

### 🏓 球速调试模式
**适用场景**: 专门调试球速计算问题

**特点**:
- 启用球速计算、摄像头同步、3D重建调试
- 显示详细的速度计算过程
- DEBUG级别日志输出
- 禁用摘要模式，显示实时详细信息

### 🎬 录制调试模式
**适用场景**: 调试录制和精彩片段功能

**特点**:
- 启用录制、精彩时刻、球速调试
- 专注于录制流程和精彩片段检测
- 显示录制状态和FFmpeg处理信息

### 🔧 开发调试模式
**适用场景**: 全面开发调试

**特点**:
- 启用大部分调试模块
- DEBUG级别详细信息
- 适合开发阶段全面调试

### 🏭 生产模式
**适用场景**: 正式部署运行

**特点**:
- 禁用所有调试输出
- 只显示WARNING及以上级别
- 每10秒输出一次摘要
- 最小化性能影响

## 🎛️ 运行时控制

### 在代码中动态切换

```cpp
// 切换特定模块调试
DebugConfig::toggleBallSpeedDebug();      // 切换球速调试
DebugConfig::toggleCameraSyncDebug();     // 切换摄像头同步调试
DebugConfig::toggleHighlightDebug();      // 切换精彩时刻调试

// 设置日志级别
DebugConfig::setLogLevel(DebugConfig::LogLevel::INFO);

// 启用/禁用摘要模式
DebugConfig::setSummaryMode(true, 5);     // 启用，5秒间隔

// 显示当前配置
DebugConfig::showCurrentConfig();
```

### 使用调试宏

```cpp
// 模块调试宏
DEBUG_BALL_SPEED("球速计算: " + std::to_string(speed) + " m/s");
DEBUG_CAMERA_SYNC("左右摄像头时间差: " + std::to_string(time_diff) + "ms");
DEBUG_HIGHLIGHT("检测到精彩时刻，速度: " + std::to_string(speed));

// 日志级别宏
LOG_DEBUG("详细调试信息");
LOG_INFO("一般信息");
LOG_WARNING("警告信息");
LOG_ERROR("错误信息");
LOG_CRITICAL("严重错误");
```

## 📋 调试输出说明

### 球速计算调试输出

```
[🏓] 检测到可能的重复帧 - 位置变化: 0.5mm, 时间间隔: 1.2ms
[🏓] 跳过重复帧，保持当前球速
[🏓] 历史数据不足: 3/5 点，设置速度为0
[🏓] 计算详情 - 历史点: 5, 时间间隔: 4.8ms(平均), 速度分量: vx=2.1, vy=1.8, vz=0.3, 最终速度: 2.8 m/s
```

### 摄像头同步调试输出

```
[📷] 双摄像头帧不同步，时间差: 15ms > 10ms，丢弃帧
[📷] 同步成功，时间差: 3ms
```

### 日志级别输出

```
[14:23:45.123] [INFO] 球速: 12.34 m/s
[14:23:45.124] [WARNING] 异常时间间隔检测 - 最小: 2.1ms, 最大: 8.9ms, 平均: 4.8ms
[14:23:45.125] [ERROR] SG Filter speed calculation error: Invalid window size
```

## 🎯 推荐使用策略

### 日常开发
1. 使用**摘要模式**作为默认配置
2. 遇到特定问题时切换到对应的专用调试模式
3. 通过日志级别控制信息详细程度

### 问题排查
1. **球速不准确** → 使用球速调试模式
2. **录制失败** → 使用录制调试模式
3. **系统整体问题** → 使用开发调试模式

### 性能测试
1. 使用生产模式测试最终性能
2. 通过摘要信息监控系统状态
3. 只在出现问题时启用详细调试

## 🔧 自定义配置

### 创建自定义调试模式

```cpp
// 在DebugConfig.cpp中添加新的预设模式
void setCustomDebugMode() {
    DebugConfig::disableAllDebug();
    DebugConfig::enable_ball_speed_debug = true;
    DebugConfig::enable_recording_debug = true;
    DebugConfig::current_log_level = DebugConfig::LogLevel::INFO;
    DebugConfig::enable_summary_mode = true;
    DebugConfig::summary_interval_seconds = 2;
    
    std::cout << "🔧 调试配置: 自定义模式已启用" << std::endl;
}
```

### 修改默认配置

在 `Utils/DebugConfig.cpp` 中修改静态成员变量的初始值：

```cpp
// 模块调试开关 - 默认配置
bool DebugConfig::enable_ball_speed_debug = true;   // 改为默认开启
bool DebugConfig::enable_highlight_debug = true;    // 保持开启
// ... 其他配置
```

## 📈 性能影响

- **摘要模式**: 几乎无性能影响
- **INFO级别**: 轻微影响（<1%）
- **DEBUG级别**: 中等影响（2-5%）
- **全调试模式**: 较大影响（5-10%）

建议在性能敏感的场景下使用摘要模式或生产模式。

## 🐛 故障排除

### 编译错误
- 确保 `Utils/DebugConfig.cpp` 已添加到 CMakeLists.txt
- 检查头文件包含路径是否正确

### 调试信息不显示
- 检查调试开关是否启用
- 确认日志级别设置是否正确
- 验证调试宏是否正确使用

### 性能问题
- 降低日志级别到WARNING或ERROR
- 启用摘要模式替代详细调试
- 禁用不必要的调试模块

通过这个调试系统，您可以根据具体需求灵活控制调试信息的输出，既能获得必要的调试信息，又能保持终端输出的简洁性。
